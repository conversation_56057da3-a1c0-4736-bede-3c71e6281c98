import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Video,
  Upload,
  Link,
  Trash2,
  Loader2,
  AlertTriangle,
  Youtube,
  FileVideo
} from "lucide-react";
import { toast } from "sonner";
import { useRoomVideoState } from "@/hooks/useRoomVideoState";
import { supabase, createAuthenticatedSupabaseClient } from "@/lib/supabase";
import { useAuth } from "@clerk/clerk-react";
import { extractYouTubeVideoId, isYouTubeUrl } from "@/utils/youtube";
import { ensureVideosBucket } from "@/utils/storage-setup";
import { validateVideoFile, formatFileSize, getEstimatedUploadTime } from "@/utils/file-validation";

interface VideoManagementProps {
  roomId: string;
  isOwner: boolean;
}

const VideoManagement = ({ roomId, isOwner }: VideoManagementProps) => {
  const { videoState, loading, setVideoUrl, clearVideo } = useRoomVideoState(roomId, isOwner);
  const { getToken, isSignedIn } = useAuth();
  const [showChangeVideo, setShowChangeVideo] = useState(false);
  const [showRemoveConfirm, setShowRemoveConfirm] = useState(false);
  const [inputUrl, setInputUrl] = useState("");
  const [uploadingFile, setUploadingFile] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Get authenticated Supabase client
  const getAuthenticatedClient = async () => {
    if (!isSignedIn) {
      return supabase;
    }

    try {
      const token = await getToken({ template: 'supabase' });
      if (token) {
        return createAuthenticatedSupabaseClient(token);
      }
      return supabase;
    } catch (error) {
      console.error('Error getting authenticated client:', error);
      return supabase;
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !isOwner) return;

    // Validate the file before starting upload
    const validation = validateVideoFile(file);
    if (!validation.isValid) {
      toast.error(validation.error || 'Invalid file selected');
      // Reset the file input
      if (event.target) {
        event.target.value = '';
      }
      return;
    }

    // Show warnings if any
    if (validation.warnings) {
      validation.warnings.forEach(warning => {
        toast.warning(warning);
      });
    }

    // Show upload info for large files
    if (file.size > 52428800) { // 50MB
      toast.info(`Uploading ${formatFileSize(file.size)} - estimated time: ${getEstimatedUploadTime(file.size)}`);
    }

    setUploadingFile(true);
    try {
      // Ensure the videos bucket exists (this should always return true now)
      await ensureVideosBucket();

      const client = await getAuthenticatedClient();

      // Generate a unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${roomId}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;

      // Upload file to Supabase Storage
      const { data, error } = await client.storage
        .from('videos')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        // Provide specific error messages based on error type
        if (error.message?.includes('exceeded the maximum allowed size')) {
          throw new Error(`File size exceeds the maximum limit of 500MB. Your file is ${formatFileSize(file.size)}.`);
        } else if (error.message?.includes('Invalid MIME type')) {
          throw new Error('Unsupported video format. Please use MP4, WebM, or other supported formats.');
        } else {
          throw error;
        }
      }

      // Get the public URL
      const { data: { publicUrl } } = client.storage
        .from('videos')
        .getPublicUrl(fileName);

      // Update the video state with the public URL
      setVideoUrl(publicUrl, 'file');
      toast.success(`Video uploaded successfully! (${formatFileSize(file.size)})`);
      setShowChangeVideo(false);

    } catch (error) {
      console.error('Error uploading video:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload video. Please try again.';
      toast.error(errorMessage);
    } finally {
      setUploadingFile(false);
      // Reset the file input
      if (event.target) {
        event.target.value = '';
      }
    }
  };

  const handleUrlSubmit = () => {
    if (inputUrl.trim() && isOwner) {
      const trimmedUrl = inputUrl.trim();

      // Check if it's a YouTube URL
      if (isYouTubeUrl(trimmedUrl)) {
        const videoId = extractYouTubeVideoId(trimmedUrl);
        if (videoId) {
          setVideoUrl(trimmedUrl, 'youtube', videoId);
          setShowChangeVideo(false);
          setInputUrl("");
          return;
        }
      }

      // Handle regular video URLs
      setVideoUrl(trimmedUrl, 'url');
      setShowChangeVideo(false);
      setInputUrl("");
    }
  };

  const handleRemoveVideo = async () => {
    try {
      await clearVideo();
      setShowRemoveConfirm(false);
    } catch (error) {
      console.error('Error removing video:', error);
      toast.error('Failed to remove video. Please try again.');
    }
  };

  const getVideoTypeIcon = () => {
    if (!videoState?.video_type) return <Video className="w-4 h-4" />;
    
    switch (videoState.video_type) {
      case 'youtube':
        return <Youtube className="w-4 h-4" />;
      case 'file':
        return <FileVideo className="w-4 h-4" />;
      default:
        return <Video className="w-4 h-4" />;
    }
  };

  const getVideoTypeLabel = () => {
    if (!videoState?.video_type) return 'Unknown';
    
    switch (videoState.video_type) {
      case 'youtube':
        return 'YouTube Video';
      case 'file':
        return 'Uploaded File';
      case 'url':
        return 'Video URL';
      default:
        return 'Video';
    }
  };

  const truncateUrl = (url: string, maxLength: number = 50) => {
    if (url.length <= maxLength) return url;
    return url.substring(0, maxLength) + '...';
  };

  if (!isOwner) {
    return (
      <div className="space-y-4">
        <h3 className="text-sm font-medium text-slate-900">Video Management</h3>
        <p className="text-sm text-slate-500">Only the room owner can manage videos.</p>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-4">
        <h3 className="text-sm font-medium text-slate-900">Video Management</h3>
        
        {videoState?.video_url ? (
          <div className="space-y-3">
            <div className="p-3 bg-slate-50 rounded-lg border">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    {getVideoTypeIcon()}
                    <Badge variant="secondary" className="text-xs">
                      {getVideoTypeLabel()}
                    </Badge>
                  </div>
                  <p className="text-sm text-slate-600 break-all">
                    {truncateUrl(videoState.video_url)}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button
                onClick={() => setShowChangeVideo(true)}
                variant="outline"
                size="sm"
                className="flex-1"
                disabled={loading}
              >
                <Video className="w-4 h-4 mr-2" />
                Change Video
              </Button>
              <Button
                onClick={() => setShowRemoveConfirm(true)}
                variant="outline"
                size="sm"
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                disabled={loading}
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Remove
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            <p className="text-sm text-slate-500">No video currently set for this room.</p>
            <Button
              onClick={() => setShowChangeVideo(true)}
              variant="outline"
              className="w-full"
              disabled={loading}
            >
              <Video className="w-4 h-4 mr-2" />
              Add Video
            </Button>
          </div>
        )}

        {showChangeVideo && (
          <div className="space-y-4 p-4 bg-slate-50 rounded-lg border">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-slate-900">
                {videoState?.video_url ? 'Change Video' : 'Add Video'}
              </h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setShowChangeVideo(false);
                  setInputUrl("");
                }}
              >
                Cancel
              </Button>
            </div>
            
            <div className="space-y-3">
              <div className="space-y-2">
                <Button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={uploadingFile}
                  className="w-full"
                  variant="outline"
                >
                  {uploadingFile ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="w-4 h-4 mr-2" />
                      Upload Video File
                    </>
                  )}
                </Button>
                <p className="text-xs text-slate-500 text-center">
                  Max file size: 500MB • Supported: MP4, WebM, OGG, AVI, MOV
                </p>
              </div>
              
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-slate-300" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-slate-50 px-2 text-slate-500">Or</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="video-url" className="text-sm text-slate-700">
                  Video URL (YouTube or direct link)
                </Label>
                <div className="flex gap-2">
                  <Input
                    id="video-url"
                    placeholder="Enter YouTube URL or video URL..."
                    value={inputUrl}
                    onChange={(e) => setInputUrl(e.target.value)}
                    className="flex-1"
                  />
                  <Button 
                    onClick={handleUrlSubmit} 
                    size="sm"
                    disabled={!inputUrl.trim()}
                  >
                    <Link className="w-4 h-4 mr-2" />
                    Add
                  </Button>
                </div>
              </div>
            </div>

            <input
              ref={fileInputRef}
              type="file"
              accept="video/*"
              onChange={handleFileUpload}
              className="hidden"
            />
          </div>
        )}
      </div>

      {/* Remove Video Confirmation Dialog */}
      <AlertDialog open={showRemoveConfirm} onOpenChange={setShowRemoveConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="w-5 h-5" />
              Remove Video
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove the current video from this room? 
              {videoState?.video_type === 'file' && ' The uploaded file will also be deleted from storage.'}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={loading}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleRemoveVideo}
              disabled={loading}
              className="bg-red-600 hover:bg-red-700"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Removing...
                </>
              ) : (
                "Remove Video"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default VideoManagement;
